---
layout: ../layouts/AboutLayout.astro
title: "About"
---

# 关于我

Hello, I'm **<PERSON><PERSON><PERSON><PERSON>**, a passionate developer and technology enthusiast. Welcome to my digital space where I share my thoughts, experiences, and discoveries in the world of programming and beyond.

## 我是谁

我是一名热爱技术的开发者，专注于现代 Web 开发技术栈。我相信技术的力量能够改变世界，也热衷于通过代码创造有价值的产品和解决方案。

## 技术栈

### 前端开发
- **框架**: React, Vue.js, Astro
- **语言**: JavaScript, TypeScript
- **样式**: CSS3, Tailwind CSS, Sass
- **工具**: Vite, Webpack, ESLint, Prettier

### 后端开发
- **语言**: Python, Node.js
- **框架**: FastAPI, Express.js
- **数据库**: PostgreSQL, MongoDB, Redis
- **云服务**: AWS, Vercel, Docker

### 其他技能
- **版本控制**: Git, GitHub
- **设计工具**: Figma, Adobe Creative Suite
- **操作系统**: Linux, macOS, Windows

## 兴趣爱好

除了编程，我还对以下领域充满兴趣：

- 🎨 **设计**: UI/UX 设计，追求简洁而优雅的用户体验
- 📚 **阅读**: 技术书籍、科幻小说、哲学思考
- 🎵 **音乐**: 古典音乐、电子音乐、独立音乐
- 🌱 **学习**: 持续学习新技术，保持对技术的敏感度
- ✍️ **写作**: 通过博客分享技术见解和生活感悟

## 博客理念

这个博客是我的数字花园，我在这里：

- **分享技术知识**: 记录学习过程中的心得体会
- **探讨最佳实践**: 分享项目开发中的经验和教训
- **思考技术趋势**: 对新技术和行业发展的观察和思考
- **记录成长历程**: 作为开发者的成长轨迹和反思

## 联系方式

如果您想与我交流技术话题、合作项目，或者只是想说声 "Hello"，欢迎通过以下方式联系我：

- 📧 **Email**: [<EMAIL>](mailto:<EMAIL>)
- 🐙 **GitHub**: [github.com/caelinya](https://github.com/caelinya)
- 🐦 **Twitter**: [@caelinya](https://twitter.com/caelinya)
- 💼 **LinkedIn**: [linkedin.com/in/caelinya](https://linkedin.com/in/caelinya)

## 致谢

感谢您访问我的博客！这个网站使用 [Astro](https://astro.build/) 构建，采用了 [AstroPaper](https://github.com/satnaing/astro-paper) 主题，并进行了个性化定制。

如果您对网站的技术实现感兴趣，或者发现了任何问题，欢迎在 GitHub 上提出 Issue 或 Pull Request。

---

*"The best way to predict the future is to create it."* - Peter Drucker