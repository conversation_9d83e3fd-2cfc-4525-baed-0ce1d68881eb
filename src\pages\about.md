---
layout: ../layouts/AboutLayout.astro
title: "About"
---

# 关于我

Hello, I'm **<PERSON><PERSON><PERSON><PERSON>**, a cybersecurity researcher and full-stack developer. Welcome to my digital space where I share my insights in cybersecurity, web development, and the fascinating intersection of security and technology.

## 我是谁

我是一名网络安全研究者和全栈开发者，专注于 Web 安全、渗透测试和现代开发技术。我相信安全不应该是开发的阻碍，而应该是创新的催化剂。通过深入理解攻击者的思维方式，我致力于构建更安全、更可靠的数字世界。

## 技术栈

### 网络安全
- **渗透测试**: Web 应用安全测试、网络渗透、社会工程学
- **漏洞研究**: SQL 注入、XSS、CSRF、权限提升、代码审计
- **安全工具**: Burp Suite, Metasploit, Nmap, Wireshark, OWASP ZAP
- **编程语言**: Python (安全脚本), Bash, PowerShell
- **系统安全**: Linux 安全加固、Windows 安全、容器安全

### Web 开发
- **前端**: React, Vue.js, Astro, JavaScript, TypeScript
- **后端**: Python (FastAPI, Django), Node.js, Express.js
- **数据库**: PostgreSQL, MongoDB, Redis
- **安全开发**: 安全编码实践、OWASP Top 10 防护、身份认证与授权

### 基础设施与工具
- **云安全**: AWS 安全配置、Docker 容器安全
- **DevSecOps**: CI/CD 安全集成、自动化安全测试
- **监控分析**: ELK Stack, 安全日志分析
- **操作系统**: Kali Linux, Ubuntu, CentOS, Windows Server

## 专业领域

### 网络安全研究
- 🔍 **漏洞挖掘**: 专注于 Web 应用安全漏洞的发现与分析
- 🛡️ **防护策略**: 研究和实施多层次安全防护方案
- 📊 **威胁情报**: 跟踪最新的安全威胁和攻击技术
- 🔬 **安全研究**: 深入研究新兴技术的安全风险

### 开发与安全的结合
- 🔐 **安全开发**: 将安全理念融入开发生命周期
- 🚀 **DevSecOps**: 推动安全左移，自动化安全测试
- 🏗️ **架构安全**: 从架构层面考虑安全设计
- 📝 **安全编码**: 编写安全、可靠的代码

## 兴趣爱好

除了专业技术，我还对以下领域充满兴趣：

- � **CTF 竞赛**: 参与各类网络安全竞赛，挑战技术极限
- 📚 **安全研究**: 阅读最新的安全论文和技术报告
- 🎵 **音乐**: 古典音乐、电子音乐，编程时的灵感来源
- 🌱 **持续学习**: 跟踪安全趋势，学习新的攻防技术
- ✍️ **技术写作**: 分享安全知识，推广安全意识

## 博客理念

这个博客是我的数字花园，我在这里：

- **分享安全知识**: 记录渗透测试、漏洞研究的实战经验
- **探讨安全实践**: 分享安全开发和防护的最佳实践
- **技术深度剖析**: 深入分析安全漏洞的原理和利用方法
- **安全意识普及**: 推广网络安全知识，提高安全防范意识
- **开发安全融合**: 探讨如何在开发过程中融入安全思维

## 联系方式

如果您想与我交流技术话题、合作项目，或者只是想说声 "Hello"，欢迎通过以下方式联系我：

- 📧 **Email**: [<EMAIL>](mailto:<EMAIL>)
- 🐙 **GitHub**: [github.com/caelinya](https://github.com/caelinya)
- 🐦 **Twitter**: [@caelinya](https://twitter.com/caelinya)
- 💼 **LinkedIn**: [linkedin.com/in/caelinya](https://linkedin.com/in/caelinya)

## 致谢

感谢您访问我的博客！这个网站使用 [Astro](https://astro.build/) 构建，采用了 [AstroPaper](https://github.com/satnaing/astro-paper) 主题，并进行了个性化定制。

如果您对网站的技术实现感兴趣，或者发现了任何问题，欢迎在 GitHub 上提出 Issue 或 Pull Request。

---

*"The best way to predict the future is to create it."* - Peter Drucker