---
// TableOfContents component for desktop article navigation
---

<div
  id="table-of-contents"
  class="hidden xl:block fixed z-30 max-w-[250px] max-h-[60vh] bg-background/95 backdrop-blur-sm border border-border rounded-lg overflow-hidden"
  style="left: calc((100vw - 768px) / 2 - 300px); top: 50%; transform: translateY(-50%);"
>
  <div class="relative">
    <!-- Top gradient mask -->
    <div class="absolute top-0 left-0 right-0 h-4 bg-gradient-to-b from-background/95 to-transparent pointer-events-none z-10"></div>
    
    <!-- TOC Content -->
    <div id="toc-content" class="p-4 overflow-y-auto max-h-[60vh] scrollbar-thin scrollbar-track-transparent scrollbar-thumb-border">
      <h3 class="text-sm font-semibold text-foreground mb-3 opacity-75">目录</h3>
      <nav id="toc-nav" class="space-y-1">
        <!-- TOC items will be dynamically inserted here -->
      </nav>
    </div>
    
    <!-- Bottom gradient mask -->
    <div class="absolute bottom-0 left-0 right-0 h-4 bg-gradient-to-t from-background/95 to-transparent pointer-events-none z-10"></div>
  </div>
</div>

<script>
  interface HeadingData {
    id: string;
    text: string;
    level: number;
    element: HTMLElement;
  }

  function initTableOfContents() {
    const tocContainer = document.querySelector("#table-of-contents") as HTMLElement;
    const tocNav = document.querySelector("#toc-nav") as HTMLElement;
    const tocContent = document.querySelector("#toc-content") as HTMLElement;
    
    if (!tocContainer || !tocNav || !tocContent) return;

    // Extract headings from the article content
    function extractHeadings(): HeadingData[] {
      const mainContent = document.querySelector("#main-content");
      if (!mainContent) return [];

      const headingSelectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
      const allHeadings = Array.from(mainContent.querySelectorAll(headingSelectors.join(', '))) as HTMLElement[];
      
      if (allHeadings.length === 0) return [];

      // Find the minimum heading level (e.g., if we have h2, h3, h4, min level is 2)
      const levels = allHeadings.map(h => parseInt(h.tagName.charAt(1)));
      const minLevel = Math.min(...levels);
      
      // Only include headings within 3 consecutive levels starting from minLevel
      const maxLevel = minLevel + 2;
      const filteredHeadings = allHeadings.filter(h => {
        const level = parseInt(h.tagName.charAt(1));
        return level >= minLevel && level <= maxLevel;
      });

      // Generate IDs for headings that don't have them
      return filteredHeadings.map((heading, index) => {
        if (!heading.id) {
          heading.id = `heading-${index}-${heading.textContent?.toLowerCase().replace(/\s+/g, '-').replace(/[^\w-]/g, '') || 'untitled'}`;
        }
        
        return {
          id: heading.id,
          text: heading.textContent?.trim() || '',
          level: parseInt(heading.tagName.charAt(1)),
          element: heading
        };
      });
    }

    // Generate TOC HTML
    function generateTOCHTML(headings: HeadingData[], minLevel: number): string {
      return headings.map(heading => {
        const indentLevel = heading.level - minLevel;
        const marginLeft = indentLevel * 16; // 16px per level
        
        return `
          <a 
            href="#${heading.id}" 
            class="toc-link block py-1 text-sm text-foreground/80 hover:text-accent transition-colors duration-200 leading-relaxed"
            style="margin-left: ${marginLeft}px"
            data-heading-id="${heading.id}"
          >
            ${heading.text}
          </a>
        `;
      }).join('');
    }

    // Track current heading based on scroll position
    let currentActiveId = '';
    let isScrolling = false;

    function updateActiveHeading() {
      const headings = Array.from(document.querySelectorAll('#main-content h1, #main-content h2, #main-content h3, #main-content h4, #main-content h5, #main-content h6')) as HTMLElement[];
      if (headings.length === 0) return;

      const scrollTop = window.scrollY;
      const viewportCenter = scrollTop + window.innerHeight / 2;
      
      // Find the heading closest to the viewport center
      let activeHeading = headings[0];
      let minDistance = Math.abs(activeHeading.offsetTop - viewportCenter);

      for (const heading of headings) {
        const distance = Math.abs(heading.offsetTop - viewportCenter);
        if (distance < minDistance) {
          minDistance = distance;
          activeHeading = heading;
        }
      }

      const newActiveId = activeHeading.id;
      if (newActiveId !== currentActiveId) {
        // Remove previous active state
        const prevActive = tocNav.querySelector('.toc-link.text-accent.font-medium');
        if (prevActive) {
          prevActive.classList.remove('text-accent', 'font-medium');
          prevActive.classList.add('text-foreground/80');
        }

        // Add new active state
        const newActive = tocNav.querySelector(`[data-heading-id="${newActiveId}"]`);
        if (newActive) {
          newActive.classList.remove('text-foreground/80');
          newActive.classList.add('text-accent', 'font-medium');
          
          // Scroll the TOC to keep the active item visible
          const tocRect = tocContent.getBoundingClientRect();
          const activeRect = newActive.getBoundingClientRect();
          
          if (activeRect.top < tocRect.top || activeRect.bottom > tocRect.bottom) {
            newActive.scrollIntoView({ block: 'center', behavior: 'smooth' });
          }
        }

        currentActiveId = newActiveId;
      }
    }

    // Handle smooth scrolling when clicking TOC links
    function handleTOCClick(event: Event) {
      event.preventDefault();
      const target = event.target as HTMLAnchorElement;
      const href = target.getAttribute('href');
      
      if (href && href.startsWith('#')) {
        const targetElement = document.querySelector(href);
        if (targetElement) {
          const headerHeight = 100; // Account for fixed header
          const targetTop = (targetElement as HTMLElement).offsetTop - headerHeight;
          
          window.scrollTo({
            top: targetTop,
            behavior: 'smooth'
          });
        }
      }
    }

    // Initialize the TOC
    const headings = extractHeadings();
    
    if (headings.length === 0) {
      // Hide TOC if no headings found
      tocContainer.style.display = 'none';
      return;
    }

    const minLevel = Math.min(...headings.map(h => h.level));
    tocNav.innerHTML = generateTOCHTML(headings, minLevel);

    // Add click event listeners to TOC links
    tocNav.addEventListener('click', handleTOCClick);

    // Set up scroll tracking with throttling
    function handleScroll() {
      if (!isScrolling) {
        requestAnimationFrame(() => {
          updateActiveHeading();
          isScrolling = false;
        });
        isScrolling = true;
      }
    }

    window.addEventListener('scroll', handleScroll);
    
    // Initial active heading update
    updateActiveHeading();

    // Cleanup function for view transitions
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }

  // Initialize on page load
  let cleanup: (() => void) | undefined;
  
  function init() {
    cleanup?.(); // Clean up previous instance
    cleanup = initTableOfContents();
  }

  init();

  // Reinitialize on view transitions
  document.addEventListener("astro:after-swap", init);
</script>

<style>
  /* Custom scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
  }
  
  .scrollbar-track-transparent::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thumb-border::-webkit-scrollbar-thumb {
    background-color: var(--border);
    border-radius: 2px;
  }
  
  .scrollbar-thumb-border::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent);
  }
</style>
